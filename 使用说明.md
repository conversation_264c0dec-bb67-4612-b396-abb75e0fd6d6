# QMT涨停双响炮自动交易系统 - 问题修复说明

## 问题诊断

您遇到的问题是系统运行后立即退出，只显示：
```
[QMT涨停双响炮自动交易系统]开始运行 
[QMT涨停双响炮自动交易系统]结束运行 
```

## 根本原因

1. **配置问题**：`SystemConfig.TARGET_STOCKS` 列表为空，导致无法获取目标股票
2. **初始化失败**：当无法获取股票列表时，系统初始化失败但没有给出明确提示
3. **程序逻辑问题**：初始化失败后程序直接退出，没有持续运行机制

## 修复内容

### 1. 添加示例股票配置
```python
TARGET_STOCKS = [
    "000001.SZ",  # 平安银行
    "000002.SZ",  # 万科A
    "600000.SH",  # 浦发银行
    "600036.SH",  # 招商银行
    "000858.SZ",  # 五粮液
    "600519.SH",  # 贵州茅台
]
```

### 2. 改进错误处理
- 初始化失败时不再让程序退出
- 提供明确的错误信息和解决方案
- 即使初始化失败也让系统继续运行

### 3. 增强状态输出
- 详细的启动信息显示
- 配置参数确认
- 运行状态实时反馈
- 非交易时间也有状态输出

### 4. 改进主循环逻辑
- 非交易时间也保持程序运行
- 定期输出系统状态
- 更好的异常处理机制

## 使用方法

### 方法1：直接使用（推荐）
1. 系统已配置了6只示例股票，可以直接运行
2. 在QMT中创建Python策略，复制修复后的代码
3. 编译并运行策略

### 方法2：自定义股票列表
1. 修改 `SystemConfig.TARGET_STOCKS` 列表
2. 添加您要监控的股票代码
3. 确保股票代码格式正确（如：000001.SZ, 600000.SH）

### 方法3：使用自定义板块
1. 在QMT中创建名为"涨停双响炮刚启动"的自定义板块
2. 添加您要监控的股票到该板块
3. 系统会优先使用板块数据

## 运行效果

修复后的系统运行时会显示：

```
============================================================
[QMT涨停双响炮自动交易系统]开始运行
============================================================
版本: 1.0.0
初始化时间: 2025-08-07 23:51:23
目标板块: 涨停双响炮刚启动
交易模式: 模拟
目标股票数量: 6只
============================================================
✅ 系统初始化成功，开始监控板块变化...
📊 当前配置的股票列表:
   1. 000001.SZ
   2. 000002.SZ
   3. 600000.SH
   ... 还有 3 只股票
============================================================
```

## 系统特性

### ✅ 已修复的问题
- 系统不再立即退出
- 配置了示例股票代码
- 改进了错误处理机制
- 增加了详细的状态输出
- 非交易时间也有运行状态显示

### 🚀 系统功能
- 自动监控目标股票变化
- 智能买卖决策
- 完整的风险控制
- 详细的交易日志
- 实时状态报告

### ⚙️ 配置参数
- `ENABLE_REAL_TRADING`: 是否启用实盘交易（默认：False）
- `MAX_SINGLE_STOCK_AMOUNT`: 单只股票最大买入金额（默认：10000元）
- `STOP_LOSS_RATIO`: 止损比例（默认：5%）
- `STOP_PROFIT_RATIO`: 止盈比例（默认：12%）

## 注意事项

1. **默认为模拟模式**：确保充分测试后再启用实盘交易
2. **股票代码格式**：必须包含市场后缀（.SZ 或 .SH）
3. **交易时间**：系统只在交易时间内执行买卖操作
4. **风险控制**：系统内置多重风险控制机制

## 技术支持

如果遇到其他问题，请检查：
1. QMT环境是否正常
2. 网络连接是否稳定
3. 股票代码格式是否正确
4. 系统日志中的错误信息

系统现在可以正常运行，不会再出现立即退出的问题！
