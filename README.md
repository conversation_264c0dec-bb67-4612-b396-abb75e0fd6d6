# QMT涨停双响炮自动交易系统

## 🎯 **项目概述**

基于QMT平台开发的专业自动交易系统，专门监控"涨停双响炮刚启动"自定义板块，实现智能化买卖操作。

### ✨ **核心特性**
- 🔄 **智能监控**：实时监控自定义板块股票变化
- 🤖 **自动交易**：板块变化触发自动买卖操作
- 🛡️ **风险控制**：完善的止损止盈和资金管理
- 📊 **实时监控**：详细的交易统计和状态监控
- 🎛️ **界面配置**：XML图形化参数配置界面

## 📦 **项目文件**

### 🚀 **核心文件**
- **`QMT涨停双响炮自动交易系统.py`** - 主系统文件（单文件包含所有功能）
- **`QMT涨停双响炮自动交易系统.xml`** - XML界面配置文件

### 📚 **文档文件**
- **`完整功能使用指南.md`** - 详细的使用说明和部署指南
- **`问题解决方案.md`** - 常见问题分析和解决方案

## 🚀 **快速开始**

### **第一步：部署文件**
1. 在QMT中新建Python策略
2. 复制`QMT涨停双响炮自动交易系统.py`代码到策略编辑器
3. 将`QMT涨停双响炮自动交易系统.xml`复制到QMT的`python/formulaLayout`文件夹

### **第二步：创建板块**
1. 在QMT中创建自定义板块
2. 板块名称：`涨停双响炮刚启动`
3. 添加要监控的股票

### **第三步：配置参数**
1. 在QMT策略界面配置参数
2. 建议先使用模拟模式测试
3. 编译并运行策略

## 📊 **系统功能**

### **数据获取**
- ✅ 通过QMT API获取自定义板块数据
- ✅ 多层次数据获取策略确保成功率
- ✅ 支持手动配置股票列表作为备选

### **交易执行**
- ✅ 集成XtQuantTrader API支持实盘交易
- ✅ 完整的交易回调处理机制
- ✅ 模拟和实盘模式无缝切换

### **风险控制**
- ✅ 止损止盈自动执行
- ✅ 资金使用率和仓位控制
- ✅ 每日交易次数和亏损限制
- ✅ 紧急停止机制

### **监控统计**
- ✅ 实时系统状态显示
- ✅ 详细的交易记录和统计
- ✅ 持仓管理和盈亏分析

## ⚙️ **主要配置参数**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 目标板块名称 | 涨停双响炮刚启动 | 监控的自定义板块 |
| 启用实盘交易 | False | 是否启用实盘模式 |
| 单股最大金额 | 10000 | 单只股票最大买入金额 |
| 止损比例 | 5% | 自动止损比例 |
| 止盈比例 | 12% | 自动止盈比例 |
| 每日最大亏损 | 5000 | 每日最大亏损限制 |

## 📈 **运行效果**

### **成功启动日志**
```
============================================================
涨停双响炮自动交易系统
============================================================
XML配置参数加载成功
目标板块: 涨停双响炮刚启动
交易模式: 模拟
============================================================
成功通过API获取板块数据
系统初始化成功，开始监控板块变化...
```

### **交易执行示例**
```
检测到新增股票: 000858.SZ
模拟买入: 000858.SZ, 价格: 10.50, 数量: 900, 金额: 9450.00

检测到移除股票: 600000.SH  
模拟卖出: 600000.SH, 价格: 11.20, 数量: 800, 盈亏: 560.00
```

## ⚠️ **重要提醒**

### **安全建议**
1. **充分测试**：先在模拟模式下运行1-2周
2. **小额开始**：实盘时从小额资金开始测试
3. **密切监控**：关注系统运行状态和日志
4. **风险控制**：合理设置止损止盈参数

### **使用前提**
1. **QMT环境**：确保QMT平台正常运行
2. **板块创建**：在QMT中创建目标自定义板块
3. **权限确认**：确保有相应的交易权限
4. **网络连接**：保持稳定的网络连接

## 🔧 **故障排除**

### **常见问题**
1. **板块获取失败**：检查板块名称是否完全一致
2. **XML界面不显示**：确认XML文件路径正确
3. **系统初始化失败**：查看详细错误日志
4. **交易执行失败**：验证账号配置和权限

详细的问题解决方案请参考 `问题解决方案.md` 文档。

## 📞 **技术支持**

### **获取帮助**
1. 查看 `完整功能使用指南.md` 获取详细使用说明
2. 查看 `问题解决方案.md` 获取故障排除指导
3. 检查系统日志获取详细错误信息
4. 确认QMT版本和API兼容性

## 📄 **版本信息**

- **当前版本**：1.0.0
- **开发平台**：QMT (迅投极速交易终端)
- **支持市场**：沪深A股
- **更新日期**：2025-01-15

## ⚖️ **免责声明**

本系统仅供学习和研究使用。股票交易存在风险，使用者应当充分了解相关风险并承担相应责任。实盘交易前请务必进行充分测试。

---

**🚀 系统已完全就绪，可立即部署使用！**
