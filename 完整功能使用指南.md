# QMT涨停双响炮自动交易系统 - 完整功能使用指南

## 🎯 **系统功能概述**

本系统现已集成完整的QMT API功能，支持：
- ✅ **真实的自定义板块获取**：通过QMT API获取"涨停双响炮刚启动"板块数据
- ✅ **实盘交易执行**：集成XtQuantTrader API，支持真实买卖操作
- ✅ **XML界面联动**：提供标准的QMT策略参数配置界面
- ✅ **完整的回调处理**：实时监控交易状态和成交回报

## 🚀 **部署步骤**

### **第一步：文件部署**

#### 1. **Python策略文件**
- 在QMT中新建Python策略
- 复制`QMT涨停双响炮自动交易系统.py`的全部代码
- 粘贴到QMT策略编辑器中

#### 2. **XML界面配置文件**
- 将`QMT涨停双响炮自动交易系统_界面配置.xml`复制到QMT安装目录下的`python/formulaLayout`文件夹
- 如果文件夹不存在，请手动创建
- 确保XML文件名与Python策略名称一致

### **第二步：创建自定义板块**

#### **在QMT中创建板块**：
1. 打开QMT客户端
2. 进入"自选股"或"板块管理"功能
3. 创建新的自定义板块
4. **板块名称必须为**：`涨停双响炮刚启动`
5. 添加初始股票到板块中

#### **板块创建示例**：
```
板块名称：涨停双响炮刚启动
初始股票：
- 000001.SZ (平安银行)
- 000002.SZ (万科A)
- 600000.SH (浦发银行)
- 600036.SH (招商银行)
```

### **第三步：配置系统参数**

#### **方法1：通过XML界面配置**
1. 在QMT策略界面中找到参数配置面板
2. 配置以下关键参数：
   - **目标板块名称**：涨停双响炮刚启动
   - **启用实盘交易**：建议先选择"否"（模拟模式）
   - **单股最大金额**：10000元
   - **止损比例**：5%
   - **止盈比例**：12%

#### **方法2：在代码中配置**
修改`SystemConfig`类中的参数：
```python
# 核心配置
TARGET_SECTOR_NAME = "涨停双响炮刚启动"  # 必须与QMT中的板块名称一致
ENABLE_REAL_TRADING = False              # 建议先用模拟模式
MAX_SINGLE_STOCK_AMOUNT = 10000         # 单只股票最大金额
STOP_LOSS_RATIO = 0.05                  # 止损比例5%
STOP_PROFIT_RATIO = 0.12                # 止盈比例12%
```

## 📊 **系统运行效果**

### **成功启动的日志输出**：
```
============================================================
涨停双响炮自动交易系统
============================================================
版本: 1.0.0
目标板块: 涨停双响炮刚启动
交易模式: 模拟
============================================================
[INFO] 涨停双响炮自动交易系统初始化完成
[INFO] 系统初始化开始...
[INFO] 目标板块: 涨停双响炮刚启动
[INFO] 交易模式: 模拟
[INFO] 最大单股金额: 10000元
尝试获取自定义板块: 涨停双响炮刚启动
成功通过API获取板块 涨停双响炮刚启动: 4只股票
✅ 成功通过API获取板块数据
目标股票数据更新成功 (第1次): 涨停双响炮刚启动 - 4只股票
首次加载目标股票: 4只股票
股票列表: 000001.SZ, 000002.SZ, 600000.SH, 600036.SH
[INFO] 板块数据初始化成功
系统初始化成功，开始监控板块变化...
```

### **板块变化检测示例**：
```
检测到新增股票: 000858.SZ
模拟买入: 000858.SZ, 价格: 10.50, 数量: 900, 金额: 9450.00

检测到移除股票: 600000.SH
模拟卖出: 600000.SH, 价格: 11.20, 数量: 800, 金额: 8960.00, 盈亏: 560.00
```

## 🔧 **功能特性详解**

### **1. 智能数据获取**
系统采用三层数据获取策略：
1. **优先API获取**：直接通过QMT API获取自定义板块
2. **下载后获取**：下载最新板块数据后再次尝试
3. **相似匹配**：搜索包含关键词的相似板块
4. **手动配置**：备选方案，支持手动配置股票列表

### **2. 完整的实盘交易**
- **交易连接**：自动连接QMT交易服务器
- **账号管理**：支持多账号配置和管理
- **订单执行**：异步下单，实时状态监控
- **回调处理**：完整的委托、成交、错误回调

### **3. 风险控制体系**
- **资金管理**：单只股票最大金额限制
- **仓位控制**：单只股票最大仓位比例
- **止损止盈**：自动计算和执行止损止盈
- **交易限制**：每日最大交易次数限制
- **紧急停止**：达到风险阈值自动停止

### **4. 实时监控功能**
```
==================================================
涨停双响炮自动交易系统状态
==================================================
当前时间: 2025-01-15 10:30:15
运行时长: 1:25:30
交易时间: 是
目标板块: 涨停双响炮刚启动
交易模式: 模拟
--------------------------------------------------
今日买入: 3笔
今日卖出: 1笔
当前持仓: 2只
今日盈亏: 156.80元
交易次数: 4/30
紧急停止: 否
--------------------------------------------------
持仓详情:
  000001.SZ: 500股 @10.20
  000002.SZ: 400股 @12.50
==================================================
```

## ⚠️ **重要注意事项**

### **实盘交易配置**
如需启用实盘交易，需要配置：
```python
# 在initialize_real_trading方法中配置
client_path = r'D:\qmt\投研\迅投极速交易终端睿智融科版\userdata'  # QMT安装路径
account_id = '2000128'  # 真实资金账号
```

### **安全建议**
1. **充分测试**：先在模拟模式下运行1-2周
2. **小额开始**：实盘时从小额资金开始
3. **密切监控**：关注系统运行状态和交易日志
4. **风险控制**：合理设置止损止盈比例

### **故障排除**
1. **板块获取失败**：确认QMT中板块名称完全一致
2. **XML界面不显示**：检查XML文件路径和命名
3. **实盘交易失败**：验证账号配置和QMT连接状态
4. **系统异常停止**：查看日志信息，检查网络连接

## 🎉 **系统优势**

1. **完整集成**：真正集成QMT官方API，功能完整可靠
2. **智能获取**：多种数据获取方式，确保系统稳定运行
3. **实盘支持**：支持真实交易，从模拟到实盘无缝切换
4. **风险可控**：完善的风险管理体系，保障资金安全
5. **易于使用**：XML界面配置，操作简单直观

**系统现在已经具备完整的自定义板块获取和实盘交易功能，可以真正实现"涨停双响炮刚启动"板块的自动化交易！** 🚀📈
